import json
from collections import Counter, defaultdict

# Read both training and validation data
training_data = []
validation_data = []

with open('data/training/entity_training_data.jsonl', 'r') as f:
    training_data = [json.loads(line) for line in f]

with open('data/training/entity_validation_data.jsonl', 'r') as f:
    validation_data = [json.loads(line) for line in f]

print("Complete Entity Dataset Analysis (Training + Validation)")
print("=" * 65)

# Count all entities from both datasets
all_entities = []
entity_examples = defaultdict(list)

# Process training data
for line in training_data:
    text = line['text']
    entities = line['entities']
    for entity in entities:
        entity_type = entity[2]
        all_entities.append(entity_type)
        if len(entity_examples[entity_type]) < 2:  # Keep up to 2 examples per entity type
            entity_examples[entity_type].append({
                'text': text,
                'entity': entity,
                'dataset': 'training'
            })

# Process validation data
for line in validation_data:
    text = line['text']
    entities = line['entities']
    for entity in entities:
        entity_type = entity[2]
        all_entities.append(entity_type)
        if len(entity_examples[entity_type]) < 4:  # Keep up to 4 total examples (2 from each dataset)
            entity_examples[entity_type].append({
                'text': text,
                'entity': entity,
                'dataset': 'validation'
            })

entity_counts = Counter(all_entities)

print(f"Training examples: {len(training_data)}")
print(f"Validation examples: {len(validation_data)}")
print(f"Total examples: {len(training_data) + len(validation_data)}")
print(f"Total entities: {len(all_entities)}")
print(f"Unique entity types: {len(entity_counts)}")
print()

print("Entity Types with Counts and Examples:")
print("-" * 40)

for entity_type, count in sorted(entity_counts.items()):
    print(f"\n{entity_type}: {count} occurrences")
    print("Examples:")
    for i, example in enumerate(entity_examples[entity_type]):
        entity = example['entity']
        start, end, label = entity
        text = example['text']
        dataset = example['dataset']
        highlighted = text[:start] + "[" + text[start:end] + "]" + text[end:]
        print(f"  {i+1}. \"{highlighted}\" ({dataset})")
    print() 