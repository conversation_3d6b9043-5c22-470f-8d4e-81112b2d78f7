#!/usr/bin/env python3
"""
Script to fix intent training data issues.
"""

import json
import random
from collections import Counter, defaultdict
from pathlib import Path

def fix_intent_data():
    """Fix intent training data by removing excessive duplicates and improving quality."""
    input_path = Path("data/training/intent_training_data.jsonl")
    output_path = Path("data/training/intent_training_data_fixed.jsonl")
    
    if not input_path.exists():
        print("❌ Intent training data not found!")
        return
    
    print("🔧 Fixing intent training data...")
    print("=" * 50)
    
    # Load all data
    samples = []
    with open(input_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                if 'query' in data and 'intent' in data:
                    samples.append(data)
            except Exception as e:
                print(f"⚠️ Skipping line {line_num}: {e}")
    
    print(f"📊 Loaded {len(samples)} samples")
    
    # Analyze duplicates by query
    query_to_intents = defaultdict(list)
    for sample in samples:
        query_to_intents[sample['query']].append(sample['intent'])
    
    # Find problematic duplicates
    excessive_duplicates = []
    moderate_duplicates = []
    unique_samples = []
    
    for query, intents in query_to_intents.items():
        if len(intents) > 5:  # More than 5 duplicates
            excessive_duplicates.append((query, intents))
        elif len(intents) > 1:  # 2-5 duplicates
            moderate_duplicates.append((query, intents))
        else:  # Unique
            unique_samples.append({'query': query, 'intent': intents[0]})
    
    print(f"\n📈 Duplicate analysis:")
    print(f"  - Unique queries: {len(unique_samples)}")
    print(f"  - Moderate duplicates (2-5): {len(moderate_duplicates)}")
    print(f"  - Excessive duplicates (>5): {len(excessive_duplicates)}")
    
    # Handle excessive duplicates - keep only the most common intent
    for query, intents in excessive_duplicates:
        intent_counts = Counter(intents)
        most_common_intent = intent_counts.most_common(1)[0][0]
        unique_samples.append({'query': query, 'intent': most_common_intent})
    
    # Handle moderate duplicates - keep the most common intent, but add some variation
    for query, intents in moderate_duplicates:
        intent_counts = Counter(intents)
        most_common_intent = intent_counts.most_common(1)[0][0]
        unique_samples.append({'query': query, 'intent': most_common_intent})
        
        # If there are multiple intents with similar frequency, keep one more
        if len(intent_counts) > 1:
            second_most_common = intent_counts.most_common(2)[1]
            if second_most_common[1] >= intent_counts.most_common(1)[0][1] * 0.7:  # At least 70% as frequent
                # Create a slight variation of the query
                variation = create_query_variation(query)
                unique_samples.append({'query': variation, 'intent': second_most_common[0]})
    
    print(f"\n🧹 After deduplication:")
    print(f"  - Final samples: {len(unique_samples)}")
    print(f"  - Removed {len(samples) - len(unique_samples)} duplicate samples")
    
    # Analyze final intent distribution
    intent_counts = Counter(sample['intent'] for sample in unique_samples)
    print(f"\n🎯 Final intent distribution:")
    for intent, count in intent_counts.most_common():
        print(f"  - {intent}: {count} samples")
    
    # Save fixed data
    with open(output_path, 'w', encoding='utf-8') as f:
        for sample in unique_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
    
    print(f"\n✅ Fixed data saved to: {output_path}")
    print(f"📈 Improvement: {len(unique_samples)} samples ({(len(unique_samples) / len(samples)) * 100:.1f}% of original)")
    
    # Create backup of original
    backup_path = Path("data/training/intent_training_data_backup.jsonl")
    with open(backup_path, 'w', encoding='utf-8') as f:
        for sample in samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
    
    print(f"💾 Original data backed up to: {backup_path}")
    
    return len(unique_samples)

def create_query_variation(query):
    """Create a slight variation of a query to reduce duplicates."""
    variations = [
        query.replace("Can you", "Could you"),
        query.replace("What is", "What's"),
        query.replace("How do I", "How can I"),
        query.replace("I need", "I want"),
        query.replace("Please", "").strip(),
        query + "?",
        query.replace("?", "").strip()
    ]
    
    # Return a random variation that's different from original
    for variation in variations:
        if variation != query and len(variation) >= 5:
            return variation
    
    # If no good variation found, add a prefix
    prefixes = ["Tell me about", "I'm asking about", "Can you explain"]
    return random.choice(prefixes) + " " + query

if __name__ == "__main__":
    fix_intent_data() 