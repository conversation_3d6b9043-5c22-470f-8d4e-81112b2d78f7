#!/usr/bin/env python3
"""
Script to validate the corrected training data and ensure all spans are valid.
"""

import json
from typing import List, Dict, Any, Tuple
from collections import Counter
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataValidator:
    """Validate training data for correct entity spans."""
    
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.stats = {
            'total_lines': 0,
            'total_entities': 0,
            'valid_entities': 0,
            'invalid_entities': 0,
            'entity_counts': Counter(),
            'invalid_examples': []
        }
    
    def validate_span(self, text: str, entity: List, line_num: int) -> bool:
        """Validate a single entity span."""
        if len(entity) < 3:
            logger.warning(f"Line {line_num}: Invalid entity format (need at least 3 elements): {entity}")
            return False
        
        # Handle different entity formats
        if len(entity) == 3:
            start, end, label = entity
        elif len(entity) == 4:
            if isinstance(entity[2], str):
                start, end, label = entity[0], entity[1], entity[2]
            else:
                start, end, label = entity[0], entity[2], entity[3]
        else:
            start, end, label = entity[0], entity[-2], entity[-1]
        
        # Check basic span validity
        if not isinstance(start, int) or not isinstance(end, int):
            logger.warning(f"Line {line_num}: Non-integer span indices: [{start}, {end}]")
            return False
        
        if start < 0:
            logger.warning(f"Line {line_num}: Negative start index: [{start}, {end}]")
            return False
        
        if end > len(text):
            logger.warning(f"Line {line_num}: End index exceeds text length: [{start}, {end}] for text length {len(text)}")
            return False
        
        if start >= end:
            logger.warning(f"Line {line_num}: Start index >= end index: [{start}, {end}]")
            return False
        
        # Check that the span contains meaningful text
        entity_text = text[start:end]
        if not entity_text.strip():
            logger.warning(f"Line {line_num}: Empty entity text for span [{start}, {end}]")
            return False
        
        return True
    
    def validate_line(self, line: str, line_num: int) -> Dict[str, Any]:
        """Validate a single line of training data."""
        try:
            data = json.loads(line.strip())
            text = data["text"]
            entities = data["entities"]
            
            line_stats = {
                'valid': True,
                'total_entities': len(entities),
                'valid_entities': 0,
                'invalid_entities': 0,
                'errors': []
            }
            
            for i, entity in enumerate(entities):
                self.stats['total_entities'] += 1
                
                if self.validate_span(text, entity, line_num):
                    self.stats['valid_entities'] += 1
                    line_stats['valid_entities'] += 1
                    
                    # Count entity types
                    if len(entity) >= 3:
                        label = entity[2] if len(entity) == 3 else entity[-1]
                        self.stats['entity_counts'][label] += 1
                else:
                    self.stats['invalid_entities'] += 1
                    line_stats['invalid_entities'] += 1
                    line_stats['valid'] = False
                    line_stats['errors'].append(f"Invalid entity {i}: {entity}")
            
            return line_stats
            
        except json.JSONDecodeError as e:
            logger.error(f"Line {line_num}: JSON decode error: {e}")
            return {'valid': False, 'errors': [f"JSON decode error: {e}"]}
        except KeyError as e:
            logger.error(f"Line {line_num}: Missing required key {e}")
            return {'valid': False, 'errors': [f"Missing key: {e}"]}
        except Exception as e:
            logger.error(f"Line {line_num}: Unexpected error: {e}")
            return {'valid': False, 'errors': [f"Unexpected error: {e}"]}
    
    def validate_training_data(self):
        """Main method to validate training data."""
        logger.info(f"Starting validation of training data from {self.input_file}")
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line.strip():  # Skip empty lines
                    line_stats = self.validate_line(line, line_num)
                    
                    if not line_stats['valid']:
                        self.stats['invalid_examples'].append({
                            'line_num': line_num,
                            'errors': line_stats['errors']
                        })
                
                # Progress reporting
                if line_num % 5000 == 0:
                    logger.info(f"Validated {line_num} lines...")
        
        self.print_validation_results()
        return self.stats
    
    def print_validation_results(self):
        """Print validation results."""
        logger.info("=" * 60)
        logger.info("TRAINING DATA VALIDATION RESULTS")
        logger.info("=" * 60)
        logger.info(f"Total lines processed: {self.stats['total_lines']}")
        logger.info(f"Total entities: {self.stats['total_entities']}")
        logger.info(f"Valid entities: {self.stats['valid_entities']}")
        logger.info(f"Invalid entities: {self.stats['invalid_entities']}")
        
        if self.stats['total_entities'] > 0:
            validity_rate = (self.stats['valid_entities'] / self.stats['total_entities']) * 100
            logger.info(f"Validity rate: {validity_rate:.2f}%")
        
        logger.info(f"Lines with errors: {len(self.stats['invalid_examples'])}")
        
        if self.stats['invalid_examples']:
            logger.info("\nFirst 10 invalid examples:")
            for i, example in enumerate(self.stats['invalid_examples'][:10]):
                logger.info(f"  Line {example['line_num']}: {example['errors']}")
        
        logger.info(f"\nTop 20 entity types by count:")
        for entity_type, count in self.stats['entity_counts'].most_common(20):
            logger.info(f"  {entity_type}: {count}")
        
        # Check for entity types with very low counts
        low_count_entities = [
            (entity_type, count) for entity_type, count in self.stats['entity_counts'].items()
            if count < 10
        ]
        
        if low_count_entities:
            logger.info(f"\nEntity types with very low counts (< 10):")
            for entity_type, count in low_count_entities:
                logger.info(f"  {entity_type}: {count}")
        
        # Summary
        if self.stats['invalid_entities'] == 0:
            logger.info("\n✅ ALL ENTITY SPANS ARE VALID!")
        else:
            logger.info(f"\n❌ Found {self.stats['invalid_entities']} invalid entity spans")


def validate_multiple_files():
    """Validate multiple training data files."""
    files_to_validate = [
        "data/training/entity_training_data.jsonl",
        "data/training/entity_training_data_fixed.jsonl",
        "data/training/entity_training_data_enhanced.jsonl"
    ]
    
    for file_path in files_to_validate:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"VALIDATING: {file_path}")
            logger.info(f"{'='*60}")
            
            validator = TrainingDataValidator(file_path)
            validator.validate_training_data()
            
        except FileNotFoundError:
            logger.warning(f"File not found: {file_path}")
        except Exception as e:
            logger.error(f"Error validating {file_path}: {e}")


if __name__ == "__main__":
    # Validate the enhanced training data
    validator = TrainingDataValidator("data/training/entity_training_data_enhanced.jsonl")
    validator.validate_training_data()
    
    # Optionally validate all files for comparison
    # validate_multiple_files()
