#!/usr/bin/env python3
"""
Script to validate the corrected training data and ensure all spans are valid.
"""

import json
from typing import List, Dict, Any, Tuple
from collections import Counter
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataValidator:
    """Validate training data for correct entity spans."""
    
    def __init__(self, input_file: str):
        self.input_file = input_file
        self.stats = {
            'total_lines': 0,
            'total_entities': 0,
            'valid_entities': 0,
            'invalid_entities': 0,
            'entity_counts': Counter(),
            'invalid_examples': []
        }
    
    def validate_span(self, text: str, entity: List, line_num: int) -> bool:
        """Validate a single entity span with semantic checking."""
        if len(entity) < 3:
            logger.warning(f"Line {line_num}: Invalid entity format (need at least 3 elements): {entity}")
            return False

        # Handle different entity formats
        if len(entity) == 3:
            start, end, label = entity
        elif len(entity) == 4:
            if isinstance(entity[2], str):
                start, end, label = entity[0], entity[1], entity[2]
            else:
                start, end, label = entity[0], entity[2], entity[3]
        else:
            start, end, label = entity[0], entity[-2], entity[-1]

        # Check basic span validity
        if not isinstance(start, int) or not isinstance(end, int):
            logger.warning(f"Line {line_num}: Non-integer span indices: [{start}, {end}]")
            return False

        if start < 0:
            logger.warning(f"Line {line_num}: Negative start index: [{start}, {end}]")
            return False

        if end > len(text):
            logger.warning(f"Line {line_num}: End index exceeds text length: [{start}, {end}] for text length {len(text)}")
            return False

        if start >= end:
            logger.warning(f"Line {line_num}: Start index >= end index: [{start}, {end}]")
            return False

        # Check that the span contains meaningful text
        entity_text = text[start:end]
        if not entity_text.strip():
            logger.warning(f"Line {line_num}: Empty entity text for span [{start}, {end}]")
            return False

        # CRITICAL: Semantic validation - check if extracted text makes sense for the label
        if not self.validate_semantic_correctness(entity_text, label, text, line_num):
            return False

        return True

    def validate_semantic_correctness(self, entity_text: str, label: str, full_text: str, line_num: int) -> bool:
        """Validate that the extracted entity text makes semantic sense for the given label."""
        entity_text = entity_text.strip()

        # Define expected patterns for each entity type
        semantic_validators = {
            'MONTH': self._validate_month,
            'YEAR': self._validate_year,
            'salary_component': self._validate_salary_component,
            'issue_type': self._validate_issue_type,
            'EMPLOYEE_NAME': self._validate_employee_name,
            'amount': self._validate_amount,
            'DATE': self._validate_date,
            'BENEFIT_TYPE': self._validate_benefit_type,
            'DOCUMENT_TYPE': self._validate_document_type,
            'POLICY_NAME': self._validate_policy_name,
            'DEPARTMENT': self._validate_department,
            'LEAVE_TYPE': self._validate_leave_type,
            'expense_type': self._validate_expense_type,
            'HR_PROCESS': self._validate_hr_process,
            'payslip_context': self._validate_payslip_context,
            'salary_type': self._validate_salary_type,
        }

        validator = semantic_validators.get(label)
        if validator:
            is_valid = validator(entity_text, full_text)
            if not is_valid:
                logger.warning(f"Line {line_num}: Semantic validation failed for {label}: '{entity_text}' in text: '{full_text}'")
                return False

        return True

    def _validate_month(self, entity_text: str, full_text: str) -> bool:
        """Validate month entities."""
        months = ['january', 'february', 'march', 'april', 'may', 'june',
                 'july', 'august', 'september', 'october', 'november', 'december',
                 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
        return entity_text.lower() in months

    def _validate_year(self, entity_text: str, full_text: str) -> bool:
        """Validate year entities."""
        try:
            year = int(entity_text)
            return 1900 <= year <= 2100
        except ValueError:
            return False

    def _validate_salary_component(self, entity_text: str, full_text: str) -> bool:
        """Validate salary component entities."""
        salary_keywords = ['salary', 'bonus', 'allowance', 'tax', 'pf', 'hra', 'incentive',
                          'commission', 'overtime', 'deduction', 'reimbursement', 'gratuity',
                          'professional', 'medical', 'conveyance', 'travel', 'retention',
                          'performance', 'annual', 'quarterly', 'monthly', 'basic', 'gross',
                          'net', 'ctc', 'variable', 'fixed', 'tds', 'income', 'lwf', 'esi',
                          'provident', 'fund', 'encashment', 'leave', 'el', 'earned']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in salary_keywords)

    def _validate_issue_type(self, entity_text: str, full_text: str) -> bool:
        """Validate issue type entities."""
        issue_keywords = ['incorrect', 'wrong', 'missing', 'error', 'mismatch', 'issue',
                         'problem', 'discrepancy', 'unpaid', 'overdue', 'pending', 'rejected',
                         'denied', 'declined', 'not', 'less', 'more', 'higher', 'lower',
                         'overpaid', 'underpaid', 'overstated', 'understated', 'inflated',
                         'reduced', 'missed', 'skipped', 'postponed', 'delayed', 'uncredited']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in issue_keywords)

    def _validate_employee_name(self, entity_text: str, full_text: str) -> bool:
        """Validate employee name entities."""
        # Names should contain alphabetic characters and possibly spaces
        import re
        return bool(re.match(r'^[A-Za-z\s]+$', entity_text.strip()))

    def _validate_amount(self, entity_text: str, full_text: str) -> bool:
        """Validate amount entities."""
        import re
        # Should contain numbers, possibly with currency symbols or words
        return bool(re.search(r'\d', entity_text))

    def _validate_date(self, entity_text: str, full_text: str) -> bool:
        """Validate date entities."""
        date_keywords = ['today', 'tomorrow', 'yesterday', 'monday', 'tuesday', 'wednesday',
                        'thursday', 'friday', 'saturday', 'sunday', 'week', 'month', 'day',
                        'fortnight', 'coming', 'next', 'last', 'this']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in date_keywords) or self._validate_month(entity_text, full_text)

    def _validate_benefit_type(self, entity_text: str, full_text: str) -> bool:
        """Validate benefit type entities."""
        benefit_keywords = ['insurance', 'medical', 'health', 'dental', 'vision', 'life',
                           'disability', 'retirement', 'pension', '401k', 'stock', 'options',
                           'vacation', 'sick', 'leave', 'hra', 'allowance', 'bonus', 'meal',
                           'transport', 'gym', 'wellness', 'childcare', 'education', 'training']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in benefit_keywords)

    def _validate_document_type(self, entity_text: str, full_text: str) -> bool:
        """Validate document type entities."""
        doc_keywords = ['payslip', 'statement', 'certificate', 'letter', 'form', 'slip',
                       'record', 'report', 'document', 'policy', 'contract', 'agreement',
                       'declaration', 'application', 'request', 'claim', 'bill', 'receipt',
                       'invoice', 'voucher', 'ticket', 'pass', 'card', 'id', 'badge']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in doc_keywords)

    def _validate_policy_name(self, entity_text: str, full_text: str) -> bool:
        """Validate policy name entities."""
        policy_keywords = ['policy', 'rule', 'regulation', 'guideline', 'procedure',
                          'protocol', 'standard', 'code', 'act', 'law', 'compliance',
                          'governance', 'framework', 'manual', 'handbook']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in policy_keywords) or len(entity_text) >= 2

    def _validate_department(self, entity_text: str, full_text: str) -> bool:
        """Validate department entities."""
        dept_keywords = ['hr', 'human', 'resources', 'finance', 'accounting', 'it',
                        'technology', 'engineering', 'sales', 'marketing', 'operations',
                        'legal', 'compliance', 'procurement', 'admin', 'management',
                        'executive', 'support', 'customer', 'service', 'quality',
                        'research', 'development', 'production', 'manufacturing']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in dept_keywords)

    def _validate_leave_type(self, entity_text: str, full_text: str) -> bool:
        """Validate leave type entities."""
        leave_keywords = ['leave', 'vacation', 'sick', 'personal', 'annual', 'casual',
                         'earned', 'maternity', 'paternity', 'medical', 'emergency',
                         'bereavement', 'sabbatical', 'unpaid', 'paid', 'compensatory']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in leave_keywords)

    def _validate_expense_type(self, entity_text: str, full_text: str) -> bool:
        """Validate expense type entities."""
        expense_keywords = ['travel', 'meal', 'accommodation', 'transport', 'fuel',
                           'internet', 'phone', 'mobile', 'communication', 'office',
                           'supplies', 'equipment', 'software', 'training', 'conference',
                           'medical', 'health', 'insurance', 'repair', 'maintenance',
                           'subscription', 'membership', 'food', 'expenses', 'bills']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in expense_keywords)

    def _validate_hr_process(self, entity_text: str, full_text: str) -> bool:
        """Validate HR process entities."""
        hr_keywords = ['processing', 'review', 'evaluation', 'assessment', 'appraisal',
                      'recruitment', 'hiring', 'onboarding', 'training', 'development',
                      'promotion', 'transfer', 'resignation', 'termination', 'grievance',
                      'disciplinary', 'action', 'investigation', 'compliance', 'audit',
                      'payroll', 'benefits', 'compensation', 'performance', 'management']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in hr_keywords)

    def _validate_payslip_context(self, entity_text: str, full_text: str) -> bool:
        """Validate payslip context entities."""
        payslip_keywords = ['payslip', 'salary', 'slip', 'statement', 'summary',
                           'breakdown', 'details', 'record', 'document', 'report']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in payslip_keywords)

    def _validate_salary_type(self, entity_text: str, full_text: str) -> bool:
        """Validate salary type entities."""
        salary_type_keywords = ['salary', 'wage', 'pay', 'compensation', 'remuneration',
                               'earnings', 'income', 'stipend', 'allowance', 'rate',
                               'hourly', 'monthly', 'annual', 'basic', 'gross', 'net']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in salary_type_keywords)

    def validate_line(self, line: str, line_num: int) -> Dict[str, Any]:
        """Validate a single line of training data."""
        try:
            data = json.loads(line.strip())
            text = data["text"]
            entities = data["entities"]
            
            line_stats = {
                'valid': True,
                'total_entities': len(entities),
                'valid_entities': 0,
                'invalid_entities': 0,
                'errors': []
            }
            
            for i, entity in enumerate(entities):
                self.stats['total_entities'] += 1
                
                if self.validate_span(text, entity, line_num):
                    self.stats['valid_entities'] += 1
                    line_stats['valid_entities'] += 1
                    
                    # Count entity types
                    if len(entity) >= 3:
                        label = entity[2] if len(entity) == 3 else entity[-1]
                        self.stats['entity_counts'][label] += 1
                else:
                    self.stats['invalid_entities'] += 1
                    line_stats['invalid_entities'] += 1
                    line_stats['valid'] = False
                    line_stats['errors'].append(f"Invalid entity {i}: {entity}")
            
            return line_stats
            
        except json.JSONDecodeError as e:
            logger.error(f"Line {line_num}: JSON decode error: {e}")
            return {'valid': False, 'errors': [f"JSON decode error: {e}"]}
        except KeyError as e:
            logger.error(f"Line {line_num}: Missing required key {e}")
            return {'valid': False, 'errors': [f"Missing key: {e}"]}
        except Exception as e:
            logger.error(f"Line {line_num}: Unexpected error: {e}")
            return {'valid': False, 'errors': [f"Unexpected error: {e}"]}
    
    def validate_training_data(self):
        """Main method to validate training data."""
        logger.info(f"Starting validation of training data from {self.input_file}")
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line.strip():  # Skip empty lines
                    line_stats = self.validate_line(line, line_num)
                    
                    if not line_stats['valid']:
                        self.stats['invalid_examples'].append({
                            'line_num': line_num,
                            'errors': line_stats['errors']
                        })
                
                # Progress reporting
                if line_num % 5000 == 0:
                    logger.info(f"Validated {line_num} lines...")
        
        self.print_validation_results()
        return self.stats
    
    def print_validation_results(self):
        """Print validation results."""
        logger.info("=" * 60)
        logger.info("TRAINING DATA VALIDATION RESULTS")
        logger.info("=" * 60)
        logger.info(f"Total lines processed: {self.stats['total_lines']}")
        logger.info(f"Total entities: {self.stats['total_entities']}")
        logger.info(f"Valid entities: {self.stats['valid_entities']}")
        logger.info(f"Invalid entities: {self.stats['invalid_entities']}")
        
        if self.stats['total_entities'] > 0:
            validity_rate = (self.stats['valid_entities'] / self.stats['total_entities']) * 100
            logger.info(f"Validity rate: {validity_rate:.2f}%")
        
        logger.info(f"Lines with errors: {len(self.stats['invalid_examples'])}")
        
        if self.stats['invalid_examples']:
            logger.info("\nFirst 10 invalid examples:")
            for i, example in enumerate(self.stats['invalid_examples'][:10]):
                logger.info(f"  Line {example['line_num']}: {example['errors']}")
        
        logger.info(f"\nTop 20 entity types by count:")
        for entity_type, count in self.stats['entity_counts'].most_common(20):
            logger.info(f"  {entity_type}: {count}")
        
        # Check for entity types with very low counts
        low_count_entities = [
            (entity_type, count) for entity_type, count in self.stats['entity_counts'].items()
            if count < 10
        ]
        
        if low_count_entities:
            logger.info(f"\nEntity types with very low counts (< 10):")
            for entity_type, count in low_count_entities:
                logger.info(f"  {entity_type}: {count}")
        
        # Summary
        if self.stats['invalid_entities'] == 0:
            logger.info("\n✅ ALL ENTITY SPANS ARE VALID!")
        else:
            logger.info(f"\n❌ Found {self.stats['invalid_entities']} invalid entity spans")


def validate_multiple_files():
    """Validate multiple training data files."""
    files_to_validate = [
        "data/training/entity_training_data.jsonl",
        "data/training/entity_training_data_fixed.jsonl",
        "data/training/entity_training_data_enhanced.jsonl"
    ]
    
    for file_path in files_to_validate:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"VALIDATING: {file_path}")
            logger.info(f"{'='*60}")
            
            validator = TrainingDataValidator(file_path)
            validator.validate_training_data()
            
        except FileNotFoundError:
            logger.warning(f"File not found: {file_path}")
        except Exception as e:
            logger.error(f"Error validating {file_path}: {e}")


if __name__ == "__main__":
    # Validate the enhanced training data
    validator = TrainingDataValidator("data/training/entity_training_data_enhanced.jsonl")
    validator.validate_training_data()
    
    # Optionally validate all files for comparison
    # validate_multiple_files()
