#!/usr/bin/env python3
"""
<PERSON>ript to analyze intent training data and identify data loss issues.
"""

import json
from collections import Counter
from pathlib import Path

def analyze_intent_data():
    """Analyze the intent training data to identify issues."""
    data_path = Path("data/training/intent_training_data.jsonl")
    
    if not data_path.exists():
        print("❌ Intent training data not found!")
        return
    
    print("🔍 Analyzing intent training data...")
    print("=" * 50)
    
    # Load all data
    texts = []
    intents = []
    line_errors = []
    
    with open(data_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                if 'query' in data and 'intent' in data:
                    texts.append(data['query'])
                    intents.append(data['intent'])
                else:
                    line_errors.append(f"Line {line_num}: Missing 'query' or 'intent' field")
            except json.JSONDecodeError as e:
                line_errors.append(f"Line {line_num}: JSON decode error - {e}")
            except Exception as e:
                line_errors.append(f"Line {line_num}: Other error - {e}")
    
    print(f"📊 Total lines processed: {len(texts) + len(line_errors)}")
    print(f"✅ Valid samples: {len(texts)}")
    print(f"❌ Error lines: {len(line_errors)}")
    
    if line_errors:
        print("\n🚨 Errors found:")
        for error in line_errors[:5]:  # Show first 5 errors
            print(f"  - {error}")
        if len(line_errors) > 5:
            print(f"  ... and {len(line_errors) - 5} more errors")
    
    # Analyze text lengths
    lengths = [len(text) for text in texts]
    short_texts = [text for text in texts if len(text) < 5]
    long_texts = [text for text in texts if len(text) > 200]
    
    print(f"\n📏 Length analysis:")
    print(f"  - Average length: {sum(lengths) / len(lengths):.1f} characters")
    print(f"  - Min length: {min(lengths)} characters")
    print(f"  - Max length: {max(lengths)} characters")
    print(f"  - Too short (<5 chars): {len(short_texts)} samples")
    print(f"  - Too long (>200 chars): {len(long_texts)} samples")
    
    if short_texts:
        print(f"\n📝 Examples of short texts:")
        for text in short_texts[:3]:
            print(f"  - '{text}' ({len(text)} chars)")
    
    if long_texts:
        print(f"\n📝 Examples of long texts:")
        for text in long_texts[:3]:
            print(f"  - '{text[:50]}...' ({len(text)} chars)")
    
    # Analyze duplicates
    seen = set()
    duplicates = []
    unique_texts = []
    
    for text in texts:
        if text in seen:
            duplicates.append(text)
        else:
            seen.add(text)
            unique_texts.append(text)
    
    print(f"\n🔄 Duplicate analysis:")
    print(f"  - Unique texts: {len(unique_texts)}")
    print(f"  - Duplicates: {len(duplicates)}")
    print(f"  - Duplicate rate: {len(duplicates) / len(texts) * 100:.1f}%")
    
    if duplicates:
        print(f"\n📝 Examples of duplicates:")
        duplicate_counts = Counter(duplicates)
        for text, count in duplicate_counts.most_common(3):
            print(f"  - '{text[:50]}...' (appears {count} times)")
    
    # Analyze intents
    intent_counts = Counter(intents)
    print(f"\n🎯 Intent analysis:")
    print(f"  - Total unique intents: {len(intent_counts)}")
    print(f"  - Most common intents:")
    for intent, count in intent_counts.most_common(10):
        print(f"    - {intent}: {count} samples")
    
    # Simulate cleaning process
    print(f"\n🧹 Simulating cleaning process:")
    
    # Step 1: Length filtering
    length_filtered = [text for text in texts if 5 <= len(text) <= 200]
    print(f"  - After length filtering: {len(length_filtered)} samples")
    print(f"  - Lost to length filtering: {len(texts) - len(length_filtered)} samples")
    
    # Step 2: Duplicate removal
    seen = set()
    deduplicated = []
    for text in length_filtered:
        if text not in seen:
            seen.add(text)
            deduplicated.append(text)
    
    print(f"  - After duplicate removal: {len(deduplicated)} samples")
    print(f"  - Lost to duplicate removal: {len(length_filtered) - len(deduplicated)} samples")
    
    print(f"\n📈 Final result: {len(deduplicated)} samples ({(len(deduplicated) / len(texts)) * 100:.1f}% of original)")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    if len(short_texts) > 0:
        print(f"  - Consider lowering min_query_length from 5 to 3 to keep {len([t for t in short_texts if len(t) >= 3])} more samples")
    
    if len(long_texts) > 0:
        print(f"  - Consider increasing max_query_length from 200 to 300 to keep {len([t for t in long_texts if len(t) <= 300])} more samples")
    
    if len(duplicates) > len(texts) * 0.1:  # More than 10% duplicates
        print(f"  - High duplicate rate detected. Consider data augmentation or manual review.")
    
    return {
        'total_samples': len(texts),
        'valid_samples': len(texts),
        'error_samples': len(line_errors),
        'short_samples': len(short_texts),
        'long_samples': len(long_texts),
        'duplicate_samples': len(duplicates),
        'final_samples': len(deduplicated),
        'intent_distribution': dict(intent_counts)
    }

if __name__ == "__main__":
    analyze_intent_data() 