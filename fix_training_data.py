#!/usr/bin/env python3
"""
Script to fix invalid entity spans in training data.
Identifies and corrects spans that exceed text boundaries.
"""

import json
import re
from typing import List, Tuple, Dict, Any, Optional
from collections import Counter, defaultdict
from difflib import SequenceMatcher
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataFixer:
    """Fix invalid entity spans in training data."""
    
    def __init__(self, input_file: str, output_file: str):
        self.input_file = input_file
        self.output_file = output_file
        self.stats = {
            'total_lines': 0,
            'invalid_spans': 0,
            'corrected_spans': 0,
            'uncorrectable_spans': 0,
            'entity_counts': Counter()
        }
    
    def similarity(self, a: str, b: str) -> float:
        """Calculate similarity between two strings."""
        return SequenceMatcher(None, a.lower(), b.lower()).ratio()
    
    def find_best_match(self, text: str, entity_text: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find the best matching span for entity_text in text using fuzzy matching.
        
        Args:
            text: The full text
            entity_text: The entity text to find
            threshold: Minimum similarity threshold
            
        Returns:
            Tuple of (start, end) if found, None otherwise
        """
        entity_text = entity_text.strip()
        if not entity_text:
            return None
            
        # First try exact match (case insensitive)
        text_lower = text.lower()
        entity_lower = entity_text.lower()
        
        start_idx = text_lower.find(entity_lower)
        if start_idx != -1:
            return (start_idx, start_idx + len(entity_text))
        
        # Try fuzzy matching with sliding window
        best_match = None
        best_score = 0
        
        # Try different window sizes around the entity length
        for window_size in [len(entity_text), len(entity_text) + 5, len(entity_text) - 5]:
            if window_size <= 0:
                continue
                
            for i in range(len(text) - window_size + 1):
                candidate = text[i:i + window_size]
                score = self.similarity(candidate, entity_text)
                
                if score > best_score and score >= threshold:
                    best_score = score
                    best_match = (i, i + window_size)
        
        return best_match
    
    def extract_entity_text_from_span(self, text: str, start: int, end: int) -> str:
        """Safely extract entity text from span, handling out-of-bounds indices."""
        try:
            # Clamp indices to valid range
            start = max(0, min(start, len(text)))
            end = max(start, min(end, len(text)))
            return text[start:end]
        except:
            return ""
    
    def validate_and_fix_span(self, text: str, entity: List) -> Tuple[List, bool]:
        """
        Validate and fix an entity span.
        
        Args:
            text: The full text
            entity: Entity in format [start, end, label] or variations
            
        Returns:
            Tuple of (corrected_entity, was_corrected)
        """
        if len(entity) < 3:
            logger.warning(f"Invalid entity format (need at least 3 elements): {entity}")
            return entity, False
        
        # Handle different entity formats
        if len(entity) == 3:
            start, end, label = entity
        elif len(entity) == 4:
            if isinstance(entity[2], str):
                start, end, label = entity[0], entity[1], entity[2]
            else:
                start, end, label = entity[0], entity[2], entity[3]
        else:
            start, end, label = entity[0], entity[-2], entity[-1]
        
        # Check if span is valid
        if start < 0 or end > len(text) or start >= end:
            self.stats['invalid_spans'] += 1
            
            # Try to extract the original entity text if possible
            original_entity_text = self.extract_entity_text_from_span(text, start, end)
            
            if original_entity_text.strip():
                # Try to find correct span for this entity text
                correct_span = self.find_best_match(text, original_entity_text)
                
                if correct_span:
                    corrected_entity = [correct_span[0], correct_span[1], label]
                    if len(entity) > 3:
                        corrected_entity.extend(entity[3:])  # Preserve extra elements
                    
                    logger.info(f"Corrected span for '{original_entity_text}': {entity[:3]} -> {corrected_entity[:3]}")
                    self.stats['corrected_spans'] += 1
                    return corrected_entity, True
            
            # If we can't correct it, try to find any occurrence of words from the label
            # This is a fallback for completely invalid spans
            if isinstance(label, str):
                # Look for label-related keywords in text
                label_words = re.findall(r'\w+', label.lower())
                for word in label_words:
                    if len(word) > 2:  # Skip very short words
                        match = re.search(rf'\b{re.escape(word)}\b', text, re.IGNORECASE)
                        if match:
                            corrected_entity = [match.start(), match.end(), label]
                            if len(entity) > 3:
                                corrected_entity.extend(entity[3:])
                            
                            logger.info(f"Fallback correction for label '{label}': {entity[:3]} -> {corrected_entity[:3]}")
                            self.stats['corrected_spans'] += 1
                            return corrected_entity, True
            
            # If all else fails, mark as uncorrectable
            logger.warning(f"Could not correct invalid span: {entity} for text: '{text[:50]}...'")
            self.stats['uncorrectable_spans'] += 1
            return None, False
        
        return entity, False
    
    def process_line(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Process a single line of training data."""
        try:
            data = json.loads(line.strip())
            text = data["text"]
            entities = data["entities"]
            
            corrected_entities = []
            line_corrected = False
            
            for entity in entities:
                corrected_entity, was_corrected = self.validate_and_fix_span(text, entity)
                
                if corrected_entity is not None:
                    corrected_entities.append(corrected_entity)
                    if was_corrected:
                        line_corrected = True
                    
                    # Count entity types
                    if len(corrected_entity) >= 3:
                        label = corrected_entity[2] if len(corrected_entity) == 3 else corrected_entity[-1]
                        self.stats['entity_counts'][label] += 1
            
            # Only return the line if we have valid entities
            if corrected_entities:
                return {
                    "text": text,
                    "entities": corrected_entities,
                    "_corrected": line_corrected,
                    "_line_num": line_num
                }
            else:
                logger.warning(f"Line {line_num} has no valid entities after correction")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error at line {line_num}: {e}")
            return None
        except KeyError as e:
            logger.error(f"Missing required key {e} at line {line_num}")
            return None
        except Exception as e:
            logger.error(f"Error processing line {line_num}: {e}")
            return None
    
    def fix_training_data(self):
        """Main method to fix training data."""
        logger.info(f"Starting to fix training data from {self.input_file}")
        
        corrected_data = []
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line.strip():  # Skip empty lines
                    processed_line = self.process_line(line, line_num)
                    if processed_line:
                        corrected_data.append(processed_line)
                
                # Progress reporting
                if line_num % 1000 == 0:
                    logger.info(f"Processed {line_num} lines...")
        
        # Write corrected data
        logger.info(f"Writing corrected data to {self.output_file}")
        with open(self.output_file, 'w', encoding='utf-8') as f:
            for item in corrected_data:
                # Remove metadata before writing
                output_item = {
                    "text": item["text"],
                    "entities": item["entities"]
                }
                f.write(json.dumps(output_item, ensure_ascii=False) + '\n')
        
        self.print_statistics()
        return corrected_data

    def print_statistics(self):
        """Print processing statistics."""
        logger.info("=" * 50)
        logger.info("TRAINING DATA CORRECTION STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Total lines processed: {self.stats['total_lines']}")
        logger.info(f"Invalid spans found: {self.stats['invalid_spans']}")
        logger.info(f"Successfully corrected: {self.stats['corrected_spans']}")
        logger.info(f"Uncorrectable spans: {self.stats['uncorrectable_spans']}")
        logger.info(f"Correction rate: {self.stats['corrected_spans'] / max(1, self.stats['invalid_spans']) * 100:.1f}%")

        logger.info("\nEntity type distribution:")
        for entity_type, count in self.stats['entity_counts'].most_common():
            logger.info(f"  {entity_type}: {count}")

        # Identify entity types with low counts
        low_count_entities = [entity for entity, count in self.stats['entity_counts'].items() if count < 10]
        if low_count_entities:
            logger.info(f"\nEntity types with low counts (< 10): {low_count_entities}")


if __name__ == "__main__":
    # Configuration
    input_file = "data/training/entity_training_data.jsonl"
    output_file = "data/training/entity_training_data_fixed.jsonl"

    # Create fixer and run
    fixer = TrainingDataFixer(input_file, output_file)
    corrected_data = fixer.fix_training_data()

    logger.info(f"Training data correction completed. Fixed data saved to {output_file}")
