#!/usr/bin/env python3
"""
Script to fix invalid entity spans in training data.
Identifies and corrects spans that exceed text boundaries.
"""

import json
import re
from typing import List, Tuple, Dict, Any, Optional
from collections import Counter, defaultdict
from difflib import SequenceMatcher
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataFixer:
    """Fix invalid entity spans in training data."""
    
    def __init__(self, input_file: str, output_file: str):
        self.input_file = input_file
        self.output_file = output_file
        self.stats = {
            'total_lines': 0,
            'total_entities': 0,
            'invalid_spans': 0,
            'empty_spans': 0,
            'semantic_errors': 0,
            'corrected_spans': 0,
            'unfixable_spans': 0,
            'uncorrectable_spans': 0,
            'lines_with_corrections': 0,
            'entity_counts': Counter()
        }
    
    def similarity(self, a: str, b: str) -> float:
        """Calculate similarity between two strings."""
        return SequenceMatcher(None, a.lower(), b.lower()).ratio()
    
    def find_best_match(self, text: str, entity_text: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        Find the best matching span for entity_text in text using fuzzy matching.
        
        Args:
            text: The full text
            entity_text: The entity text to find
            threshold: Minimum similarity threshold
            
        Returns:
            Tuple of (start, end) if found, None otherwise
        """
        entity_text = entity_text.strip()
        if not entity_text:
            return None
            
        # First try exact match (case insensitive)
        text_lower = text.lower()
        entity_lower = entity_text.lower()
        
        start_idx = text_lower.find(entity_lower)
        if start_idx != -1:
            return (start_idx, start_idx + len(entity_text))
        
        # Try fuzzy matching with sliding window
        best_match = None
        best_score = 0
        
        # Try different window sizes around the entity length
        for window_size in [len(entity_text), len(entity_text) + 5, len(entity_text) - 5]:
            if window_size <= 0:
                continue
                
            for i in range(len(text) - window_size + 1):
                candidate = text[i:i + window_size]
                score = self.similarity(candidate, entity_text)
                
                if score > best_score and score >= threshold:
                    best_score = score
                    best_match = (i, i + window_size)
        
        return best_match
    
    def extract_entity_text_from_span(self, text: str, start: int, end: int) -> str:
        """Safely extract entity text from span, handling out-of-bounds indices."""
        try:
            # Clamp indices to valid range
            start = max(0, min(start, len(text)))
            end = max(start, min(end, len(text)))
            return text[start:end]
        except:
            return ""
    
    def validate_and_fix_span(self, text: str, entity: List, line_num: int = 0) -> Tuple[List, bool]:
        """
        Validate and fix an entity span using semantic analysis.

        Args:
            text: The full text
            entity: Entity in format [start, end, label] or variations
            line_num: Line number for logging

        Returns:
            Tuple of (corrected_entity, was_corrected)
        """
        if len(entity) < 3:
            logger.warning(f"Line {line_num}: Invalid entity format (need at least 3 elements): {entity}")
            return entity, False

        # Handle different entity formats
        if len(entity) == 3:
            start, end, label = entity
        elif len(entity) == 4:
            if isinstance(entity[2], str):
                start, end, label = entity[0], entity[1], entity[2]
            else:
                start, end, label = entity[0], entity[2], entity[3]
        else:
            start, end, label = entity[0], entity[-2], entity[-1]

        # Check basic span validity first
        if start < 0 or end > len(text) or start >= end:
            self.stats['invalid_spans'] += 1
            logger.warning(f"Line {line_num}: Invalid span bounds [{start}, {end}] for text length {len(text)}")

            # Try to find the correct entity in the text
            corrected_entity = self.find_correct_entity_span(text, label, entity)
            if corrected_entity:
                self.stats['corrected_spans'] += 1
                return corrected_entity, True
            else:
                self.stats['unfixable_spans'] += 1
                return entity, False

        # Check semantic validity
        entity_text = text[start:end].strip()
        if not entity_text:
            self.stats['empty_spans'] += 1
            logger.warning(f"Line {line_num}: Empty entity text for span [{start}, {end}]")

            # Try to find the correct entity in the text
            corrected_entity = self.find_correct_entity_span(text, label, entity)
            if corrected_entity:
                self.stats['corrected_spans'] += 1
                return corrected_entity, True
            else:
                self.stats['unfixable_spans'] += 1
                return entity, False

        # Check if the extracted text makes sense for the label
        if not self.validate_semantic_correctness(entity_text, label, text):
            self.stats['semantic_errors'] += 1
            logger.warning(f"Line {line_num}: Semantic validation failed for {label}: '{entity_text}' in text: '{text[:100]}...'")

            # Try to find the correct entity in the text
            corrected_entity = self.find_correct_entity_span(text, label, entity)
            if corrected_entity:
                self.stats['corrected_spans'] += 1
                return corrected_entity, True
            else:
                self.stats['unfixable_spans'] += 1
                return entity, False
            
            # If we can't correct it, try to find any occurrence of words from the label
            # This is a fallback for completely invalid spans
            if isinstance(label, str):
                # Look for label-related keywords in text
                label_words = re.findall(r'\w+', label.lower())
                for word in label_words:
                    if len(word) > 2:  # Skip very short words
                        match = re.search(rf'\b{re.escape(word)}\b', text, re.IGNORECASE)
                        if match:
                            corrected_entity = [match.start(), match.end(), label]
                            if len(entity) > 3:
                                corrected_entity.extend(entity[3:])
                            
                            logger.info(f"Fallback correction for label '{label}': {entity[:3]} -> {corrected_entity[:3]}")
                            self.stats['corrected_spans'] += 1
                            return corrected_entity, True
            
            # If all else fails, mark as uncorrectable
            logger.warning(f"Could not correct invalid span: {entity} for text: '{text[:50]}...'")
            self.stats['uncorrectable_spans'] += 1
            return None, False
        
        return entity, False

    def validate_semantic_correctness(self, entity_text: str, label: str, full_text: str) -> bool:
        """Validate that the extracted entity text makes semantic sense for the given label."""
        entity_text = entity_text.strip()

        # Define expected patterns for each entity type
        semantic_validators = {
            'MONTH': self._validate_month,
            'YEAR': self._validate_year,
            'salary_component': self._validate_salary_component,
            'issue_type': self._validate_issue_type,
            'EMPLOYEE_NAME': self._validate_employee_name,
            'amount': self._validate_amount,
            'DATE': self._validate_date,
            'BENEFIT_TYPE': self._validate_benefit_type,
            'DOCUMENT_TYPE': self._validate_document_type,
            'POLICY_NAME': self._validate_policy_name,
            'DEPARTMENT': self._validate_department,
            'LEAVE_TYPE': self._validate_leave_type,
            'expense_type': self._validate_expense_type,
            'HR_PROCESS': self._validate_hr_process,
            'payslip_context': self._validate_payslip_context,
            'salary_type': self._validate_salary_type,
        }

        validator = semantic_validators.get(label)
        if validator:
            return validator(entity_text, full_text)

        return True  # If no specific validator, assume valid

    def find_correct_entity_span(self, text: str, label: str, original_entity: List) -> List:
        """Find the correct span for an entity based on its label and context."""
        # Get potential candidates based on label type
        candidates = self.find_entity_candidates(text, label)

        if not candidates:
            return None

        # If only one candidate, use it
        if len(candidates) == 1:
            start, end = candidates[0]
            corrected_entity = [start, end, label]
            if len(original_entity) > 3:
                corrected_entity.extend(original_entity[3:])
            return corrected_entity

        # If multiple candidates, try to pick the best one
        # For now, pick the first valid one (can be improved with more sophisticated logic)
        for start, end in candidates:
            entity_text = text[start:end].strip()
            if self.validate_semantic_correctness(entity_text, label, text):
                corrected_entity = [start, end, label]
                if len(original_entity) > 3:
                    corrected_entity.extend(original_entity[3:])
                return corrected_entity

        return None

    def find_entity_candidates(self, text: str, label: str) -> List[Tuple[int, int]]:
        """Find potential entity spans in text based on label type."""
        import re
        candidates = []

        if label == 'MONTH':
            months = ['january', 'february', 'march', 'april', 'may', 'june',
                     'july', 'august', 'september', 'october', 'november', 'december',
                     'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
            for month in months:
                for match in re.finditer(r'\b' + re.escape(month) + r'\b', text, re.IGNORECASE):
                    candidates.append((match.start(), match.end()))

        elif label == 'YEAR':
            for match in re.finditer(r'\b(19|20)\d{2}\b', text):
                candidates.append((match.start(), match.end()))

        elif label == 'salary_component':
            salary_keywords = ['salary', 'bonus', 'allowance', 'tax', 'pf', 'hra', 'incentive',
                              'commission', 'overtime', 'deduction', 'reimbursement', 'gratuity',
                              'professional tax', 'medical', 'conveyance', 'travel', 'retention',
                              'performance', 'annual', 'quarterly', 'monthly', 'basic', 'gross',
                              'net', 'ctc', 'variable', 'fixed', 'tds', 'income tax', 'lwf', 'esi',
                              'provident fund', 'encashment', 'leave', 'el', 'earned leave']
            for keyword in salary_keywords:
                for match in re.finditer(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE):
                    candidates.append((match.start(), match.end()))

        elif label == 'issue_type':
            issue_keywords = ['incorrect', 'wrong', 'missing', 'error', 'mismatch', 'issue',
                             'problem', 'discrepancy', 'unpaid', 'overdue', 'pending', 'rejected',
                             'denied', 'declined', 'not processed', 'not included', 'not credited',
                             'overpaid', 'underpaid', 'overstated', 'understated', 'inflated',
                             'reduced', 'missed', 'skipped', 'postponed', 'delayed', 'uncredited']
            for keyword in issue_keywords:
                for match in re.finditer(r'\b' + re.escape(keyword) + r'\b', text, re.IGNORECASE):
                    candidates.append((match.start(), match.end()))

        elif label == 'EMPLOYEE_NAME':
            # Look for capitalized words that could be names
            for match in re.finditer(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b', text):
                candidates.append((match.start(), match.end()))

        elif label == 'amount':
            # Look for numbers with currency indicators
            for match in re.finditer(r'\b\d+[,\d]*\.?\d*\b', text):
                candidates.append((match.start(), match.end()))

        # Add more specific patterns for other entity types as needed

        return candidates

    def _validate_month(self, entity_text: str, full_text: str) -> bool:
        """Validate month entities."""
        months = ['january', 'february', 'march', 'april', 'may', 'june',
                 'july', 'august', 'september', 'october', 'november', 'december',
                 'jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']
        return entity_text.lower() in months

    def _validate_year(self, entity_text: str, full_text: str) -> bool:
        """Validate year entities."""
        try:
            year = int(entity_text)
            return 1900 <= year <= 2100
        except ValueError:
            return False

    def _validate_salary_component(self, entity_text: str, full_text: str) -> bool:
        """Validate salary component entities."""
        salary_keywords = ['salary', 'bonus', 'allowance', 'tax', 'pf', 'hra', 'incentive',
                          'commission', 'overtime', 'deduction', 'reimbursement', 'gratuity',
                          'professional', 'medical', 'conveyance', 'travel', 'retention',
                          'performance', 'annual', 'quarterly', 'monthly', 'basic', 'gross',
                          'net', 'ctc', 'variable', 'fixed', 'tds', 'income', 'lwf', 'esi',
                          'provident', 'fund', 'encashment', 'leave', 'el', 'earned']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in salary_keywords)

    def _validate_issue_type(self, entity_text: str, full_text: str) -> bool:
        """Validate issue type entities."""
        issue_keywords = ['incorrect', 'wrong', 'missing', 'error', 'mismatch', 'issue',
                         'problem', 'discrepancy', 'unpaid', 'overdue', 'pending', 'rejected',
                         'denied', 'declined', 'not', 'less', 'more', 'higher', 'lower',
                         'overpaid', 'underpaid', 'overstated', 'understated', 'inflated',
                         'reduced', 'missed', 'skipped', 'postponed', 'delayed', 'uncredited']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in issue_keywords)

    def _validate_employee_name(self, entity_text: str, full_text: str) -> bool:
        """Validate employee name entities."""
        # Names should contain alphabetic characters and possibly spaces
        import re
        return bool(re.match(r'^[A-Za-z\s]+$', entity_text.strip()))

    def _validate_amount(self, entity_text: str, full_text: str) -> bool:
        """Validate amount entities."""
        import re
        # Should contain numbers, possibly with currency symbols or words
        return bool(re.search(r'\d', entity_text))

    def _validate_date(self, entity_text: str, full_text: str) -> bool:
        """Validate date entities."""
        date_keywords = ['today', 'tomorrow', 'yesterday', 'monday', 'tuesday', 'wednesday',
                        'thursday', 'friday', 'saturday', 'sunday', 'week', 'month', 'day',
                        'fortnight', 'coming', 'next', 'last', 'this']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in date_keywords) or self._validate_month(entity_text, full_text)

    def _validate_benefit_type(self, entity_text: str, full_text: str) -> bool:
        """Validate benefit type entities."""
        benefit_keywords = ['insurance', 'medical', 'health', 'dental', 'vision', 'life',
                           'disability', 'retirement', 'pension', '401k', 'stock', 'options',
                           'vacation', 'sick', 'leave', 'hra', 'allowance', 'bonus', 'meal',
                           'transport', 'gym', 'wellness', 'childcare', 'education', 'training']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in benefit_keywords)

    def _validate_document_type(self, entity_text: str, full_text: str) -> bool:
        """Validate document type entities."""
        doc_keywords = ['payslip', 'statement', 'certificate', 'letter', 'form', 'slip',
                       'record', 'report', 'document', 'policy', 'contract', 'agreement',
                       'declaration', 'application', 'request', 'claim', 'bill', 'receipt',
                       'invoice', 'voucher', 'ticket', 'pass', 'card', 'id', 'badge']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in doc_keywords)

    def _validate_policy_name(self, entity_text: str, full_text: str) -> bool:
        """Validate policy name entities."""
        policy_keywords = ['policy', 'rule', 'regulation', 'guideline', 'procedure',
                          'protocol', 'standard', 'code', 'act', 'law', 'compliance',
                          'governance', 'framework', 'manual', 'handbook']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in policy_keywords) or len(entity_text) >= 2

    def _validate_department(self, entity_text: str, full_text: str) -> bool:
        """Validate department entities."""
        dept_keywords = ['hr', 'human', 'resources', 'finance', 'accounting', 'it',
                        'technology', 'engineering', 'sales', 'marketing', 'operations',
                        'legal', 'compliance', 'procurement', 'admin', 'management',
                        'executive', 'support', 'customer', 'service', 'quality',
                        'research', 'development', 'production', 'manufacturing']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in dept_keywords)

    def _validate_leave_type(self, entity_text: str, full_text: str) -> bool:
        """Validate leave type entities."""
        leave_keywords = ['leave', 'vacation', 'sick', 'personal', 'annual', 'casual',
                         'earned', 'maternity', 'paternity', 'medical', 'emergency',
                         'bereavement', 'sabbatical', 'unpaid', 'paid', 'compensatory']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in leave_keywords)

    def _validate_expense_type(self, entity_text: str, full_text: str) -> bool:
        """Validate expense type entities."""
        expense_keywords = ['travel', 'meal', 'accommodation', 'transport', 'fuel',
                           'internet', 'phone', 'mobile', 'communication', 'office',
                           'supplies', 'equipment', 'software', 'training', 'conference',
                           'medical', 'health', 'insurance', 'repair', 'maintenance',
                           'subscription', 'membership', 'food', 'expenses', 'bills']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in expense_keywords)

    def _validate_hr_process(self, entity_text: str, full_text: str) -> bool:
        """Validate HR process entities."""
        hr_keywords = ['processing', 'review', 'evaluation', 'assessment', 'appraisal',
                      'recruitment', 'hiring', 'onboarding', 'training', 'development',
                      'promotion', 'transfer', 'resignation', 'termination', 'grievance',
                      'disciplinary', 'action', 'investigation', 'compliance', 'audit',
                      'payroll', 'benefits', 'compensation', 'performance', 'management']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in hr_keywords)

    def _validate_payslip_context(self, entity_text: str, full_text: str) -> bool:
        """Validate payslip context entities."""
        payslip_keywords = ['payslip', 'salary', 'slip', 'statement', 'summary',
                           'breakdown', 'details', 'record', 'document', 'report']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in payslip_keywords)

    def _validate_salary_type(self, entity_text: str, full_text: str) -> bool:
        """Validate salary type entities."""
        salary_type_keywords = ['salary', 'wage', 'pay', 'compensation', 'remuneration',
                               'earnings', 'income', 'stipend', 'allowance', 'rate',
                               'hourly', 'monthly', 'annual', 'basic', 'gross', 'net']
        text_lower = entity_text.lower()
        return any(keyword in text_lower for keyword in salary_type_keywords)

    def process_line(self, line: str, line_num: int) -> Optional[Dict[str, Any]]:
        """Process a single line of training data."""
        try:
            data = json.loads(line.strip())
            text = data["text"]
            entities = data["entities"]
            
            corrected_entities = []
            line_corrected = False
            
            for entity in entities:
                self.stats['total_entities'] += 1
                corrected_entity, was_corrected = self.validate_and_fix_span(text, entity, line_num)

                if corrected_entity is not None:
                    corrected_entities.append(corrected_entity)
                    if was_corrected:
                        line_corrected = True
                    
                    # Count entity types
                    if len(corrected_entity) >= 3:
                        label = corrected_entity[2] if len(corrected_entity) == 3 else corrected_entity[-1]
                        self.stats['entity_counts'][label] += 1
            
            # Only return the line if we have valid entities
            if corrected_entities:
                return {
                    "text": text,
                    "entities": corrected_entities,
                    "_corrected": line_corrected,
                    "_line_num": line_num
                }
            else:
                logger.warning(f"Line {line_num} has no valid entities after correction")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error at line {line_num}: {e}")
            return None
        except KeyError as e:
            logger.error(f"Missing required key {e} at line {line_num}")
            return None
        except Exception as e:
            logger.error(f"Error processing line {line_num}: {e}")
            return None
    
    def fix_training_data(self):
        """Main method to fix training data."""
        logger.info(f"Starting to fix training data from {self.input_file}")
        
        corrected_data = []
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                self.stats['total_lines'] += 1
                
                if line.strip():  # Skip empty lines
                    processed_line = self.process_line(line, line_num)
                    if processed_line:
                        corrected_data.append(processed_line)
                
                # Progress reporting
                if line_num % 1000 == 0:
                    logger.info(f"Processed {line_num} lines...")
        
        # Write corrected data
        logger.info(f"Writing corrected data to {self.output_file}")
        with open(self.output_file, 'w', encoding='utf-8') as f:
            for item in corrected_data:
                # Remove metadata before writing
                output_item = {
                    "text": item["text"],
                    "entities": item["entities"]
                }
                f.write(json.dumps(output_item, ensure_ascii=False) + '\n')
        
        self.print_statistics()
        return corrected_data

    def print_statistics(self):
        """Print processing statistics."""
        logger.info("=" * 50)
        logger.info("TRAINING DATA CORRECTION STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Total lines processed: {self.stats['total_lines']}")
        logger.info(f"Invalid spans found: {self.stats['invalid_spans']}")
        logger.info(f"Successfully corrected: {self.stats['corrected_spans']}")
        logger.info(f"Uncorrectable spans: {self.stats['uncorrectable_spans']}")
        logger.info(f"Correction rate: {self.stats['corrected_spans'] / max(1, self.stats['invalid_spans']) * 100:.1f}%")

        logger.info("\nEntity type distribution:")
        for entity_type, count in self.stats['entity_counts'].most_common():
            logger.info(f"  {entity_type}: {count}")

        # Identify entity types with low counts
        low_count_entities = [entity for entity, count in self.stats['entity_counts'].items() if count < 10]
        if low_count_entities:
            logger.info(f"\nEntity types with low counts (< 10): {low_count_entities}")


if __name__ == "__main__":
    # Configuration
    input_file = "data/training/entity_training_data.jsonl"
    output_file = "data/training/entity_training_data_fixed.jsonl"

    # Create fixer and run
    fixer = TrainingDataFixer(input_file, output_file)
    corrected_data = fixer.fix_training_data()

    logger.info(f"Training data correction completed. Fixed data saved to {output_file}")
