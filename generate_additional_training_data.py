#!/usr/bin/env python3
"""
<PERSON>ript to generate additional training data for entity types with low counts.
Creates synthetic examples to balance the dataset.
"""

import json
import random
from typing import List, Dict, Any, Tuple
from collections import Counter
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingDataGenerator:
    """Generate additional training data for underrepresented entity types."""
    
    def __init__(self, input_file: str, output_file: str, min_count: int = 50):
        self.input_file = input_file
        self.output_file = output_file
        self.min_count = min_count
        self.entity_counts = Counter()
        self.existing_data = []
        
        # Templates for generating new data
        self.templates = {
            'AMOUNT': [
                "The salary amount of {amount} seems incorrect.",
                "I received {amount} instead of my expected salary.",
                "My bonus payment was {amount} this month.",
                "The deduction amount shows {amount} on my payslip.",
                "I was expecting {amount} but got less.",
                "The overtime payment of {amount} is missing.",
                "My increment amount is {amount} per month.",
                "The tax deduction shows {amount} which seems high.",
                "I need clarification on the {amount} charge.",
                "The reimbursement amount of {amount} is pending."
            ],
            'SALARY_COMPONENT': [
                "My {component} for this month is incorrect.",
                "The {component} calculation seems wrong.",
                "I didn't receive my {component} payment.",
                "Can you check my {component} amount?",
                "The {component} deduction is too high.",
                "My {component} is missing from the payslip.",
                "I need help with my {component} calculation.",
                "The {component} amount has changed unexpectedly.",
                "Please verify my {component} for last month.",
                "I have a question about my {component}."
            ]
        }
        
        # Sample values for different entity types
        self.sample_values = {
            'AMOUNT': [
                "₹50,000", "₹75,000", "₹1,20,000", "$5,000", "$10,000",
                "₹25,000", "₹30,000", "₹45,000", "₹60,000", "₹85,000",
                "$2,500", "$7,500", "$12,000", "₹15,000", "₹40,000"
            ],
            'SALARY_COMPONENT': [
                "basic salary", "HRA", "medical allowance", "transport allowance",
                "performance bonus", "overtime pay", "special allowance", "PF contribution",
                "ESI deduction", "professional tax", "income tax", "variable pay",
                "retention bonus", "joining bonus", "annual bonus"
            ]
        }
    
    def load_existing_data(self):
        """Load existing training data and count entity types."""
        logger.info(f"Loading existing data from {self.input_file}")
        
        with open(self.input_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    data = json.loads(line.strip())
                    self.existing_data.append(data)
                    
                    # Count entity types
                    for entity in data['entities']:
                        if len(entity) >= 3:
                            label = entity[2] if len(entity) == 3 else entity[-1]
                            self.entity_counts[label] += 1
                            
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error at line {line_num}: {e}")
                except Exception as e:
                    logger.error(f"Error processing line {line_num}: {e}")
    
    def find_low_count_entities(self) -> List[str]:
        """Find entity types with counts below the minimum threshold."""
        low_count_entities = [
            entity_type for entity_type, count in self.entity_counts.items()
            if count < self.min_count
        ]
        
        logger.info(f"Entity types with low counts (< {self.min_count}):")
        for entity_type in low_count_entities:
            logger.info(f"  {entity_type}: {self.entity_counts[entity_type]}")
        
        return low_count_entities
    
    def generate_synthetic_examples(self, entity_type: str, target_count: int) -> List[Dict[str, Any]]:
        """Generate synthetic training examples for a specific entity type."""
        if entity_type not in self.templates:
            logger.warning(f"No templates available for entity type: {entity_type}")
            return []
        
        current_count = self.entity_counts[entity_type]
        needed_count = max(0, target_count - current_count)
        
        if needed_count == 0:
            return []
        
        logger.info(f"Generating {needed_count} examples for {entity_type}")
        
        synthetic_examples = []
        templates = self.templates[entity_type]
        sample_values = self.sample_values[entity_type]
        
        for i in range(needed_count):
            # Select random template and value
            template = random.choice(templates)
            value = random.choice(sample_values)
            
            # Generate text
            if entity_type == 'AMOUNT':
                text = template.format(amount=value)
                # Find the position of the amount in the text
                start_pos = text.find(value)
                end_pos = start_pos + len(value)
                entities = [[start_pos, end_pos, entity_type]]
            
            elif entity_type == 'SALARY_COMPONENT':
                text = template.format(component=value)
                # Find the position of the component in the text
                start_pos = text.find(value)
                end_pos = start_pos + len(value)
                entities = [[start_pos, end_pos, entity_type]]
            
            # Validate the generated example
            if self.validate_example(text, entities):
                synthetic_examples.append({
                    "text": text,
                    "entities": entities
                })
            else:
                logger.warning(f"Generated invalid example for {entity_type}: {text}")
        
        return synthetic_examples
    
    def validate_example(self, text: str, entities: List[List]) -> bool:
        """Validate that the generated example has correct spans."""
        for entity in entities:
            if len(entity) < 3:
                return False
            
            start, end, label = entity[0], entity[1], entity[2]
            
            # Check span validity
            if start < 0 or end > len(text) or start >= end:
                return False
            
            # Check that the span contains meaningful text
            entity_text = text[start:end].strip()
            if not entity_text:
                return False
        
        return True
    
    def generate_additional_data(self):
        """Main method to generate additional training data."""
        logger.info("Starting additional training data generation")
        
        # Load existing data
        self.load_existing_data()
        
        # Find entity types that need more examples
        low_count_entities = self.find_low_count_entities()
        
        if not low_count_entities:
            logger.info("All entity types have sufficient examples")
            return
        
        # Generate synthetic examples
        all_synthetic_examples = []
        
        for entity_type in low_count_entities:
            synthetic_examples = self.generate_synthetic_examples(entity_type, self.min_count)
            all_synthetic_examples.extend(synthetic_examples)
        
        if not all_synthetic_examples:
            logger.info("No synthetic examples generated")
            return
        
        # Combine existing and synthetic data
        combined_data = self.existing_data + all_synthetic_examples
        
        # Shuffle the combined data
        random.shuffle(combined_data)
        
        # Write to output file
        logger.info(f"Writing {len(combined_data)} examples to {self.output_file}")
        logger.info(f"Added {len(all_synthetic_examples)} synthetic examples")
        
        with open(self.output_file, 'w', encoding='utf-8') as f:
            for item in combined_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # Print final statistics
        self.print_final_statistics(all_synthetic_examples)
    
    def print_final_statistics(self, synthetic_examples: List[Dict[str, Any]]):
        """Print final statistics about the generated data."""
        logger.info("=" * 50)
        logger.info("ADDITIONAL DATA GENERATION STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Original examples: {len(self.existing_data)}")
        logger.info(f"Synthetic examples generated: {len(synthetic_examples)}")
        logger.info(f"Total examples: {len(self.existing_data) + len(synthetic_examples)}")
        
        # Count synthetic examples by entity type
        synthetic_counts = Counter()
        for example in synthetic_examples:
            for entity in example['entities']:
                if len(entity) >= 3:
                    label = entity[2] if len(entity) == 3 else entity[-1]
                    synthetic_counts[label] += 1
        
        logger.info("\nSynthetic examples by entity type:")
        for entity_type, count in synthetic_counts.items():
            logger.info(f"  {entity_type}: {count}")


if __name__ == "__main__":
    # Configuration
    input_file = "data/training/entity_training_data_fixed.jsonl"
    output_file = "data/training/entity_training_data_enhanced.jsonl"
    min_count = 50  # Minimum number of examples per entity type
    
    # Create generator and run
    generator = TrainingDataGenerator(input_file, output_file, min_count)
    generator.generate_additional_data()
    
    logger.info(f"Enhanced training data saved to {output_file}")
