#!/usr/bin/env python3
"""
Script to count the exact number of each intent in the training file.
"""

import json
from collections import Counter
from pathlib import Path

def count_intents():
    """Count the exact number of each intent in the training file."""
    data_path = Path("data/training/intent_training_data.jsonl")
    
    if not data_path.exists():
        print("❌ Intent training data not found!")
        return
    
    print("🔢 Counting intents in training data...")
    print("=" * 50)
    
    # Load and count intents
    intents = []
    with open(data_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            try:
                data = json.loads(line.strip())
                if 'intent' in data:
                    intents.append(data['intent'])
            except Exception as e:
                print(f"⚠️ Error on line {line_num}: {e}")
    
    # Count intents
    intent_counts = Counter(intents)
    total_samples = len(intents)
    
    print(f"📊 Total samples: {total_samples}")
    print(f"🎯 Unique intents: {len(intent_counts)}")
    print("\n📈 Intent distribution:")
    print("-" * 40)
    
    # Sort by count (descending)
    for intent, count in intent_counts.most_common():
        percentage = (count / total_samples) * 100
        print(f"{intent:25} | {count:4} samples | {percentage:5.1f}%")
    
    print("-" * 40)
    print(f"{'TOTAL':25} | {total_samples:4} samples | 100.0%")
    
    return intent_counts

if __name__ == "__main__":
    count_intents() 