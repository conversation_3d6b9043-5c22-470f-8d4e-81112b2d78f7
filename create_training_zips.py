#!/usr/bin/env python3
"""
Script to create training zip files for intent and NER models.
These zip files can be used for training in Kaggle kernels.
"""

import os
import json
import zipfile
import shutil
from pathlib import Path
from typing import List, Dict, Any

def create_intent_training_zip():
    """Create zip file for intent classifier training."""
    print("Creating intent training zip...")
    
    # Create temporary directory for intent training
    intent_dir = Path("temp_intent_training")
    intent_dir.mkdir(exist_ok=True)
    
    try:
        # Copy training data
        training_data_path = Path("data/training/intent_training_data.jsonl")
        if training_data_path.exists():
            shutil.copy2(training_data_path, intent_dir / "intent_training_data.jsonl")
            print(f"✓ Copied intent training data ({training_data_path.stat().st_size} bytes)")
        else:
            print("✗ Intent training data not found")
            return False
        
        # Copy intent classifier files
        intent_files = [
            "src/intent/train_classifier.py",
            "src/intent/config.yaml",
            "src/intent/config_runtime.yaml",
            "src/intent/intent_classifier.py",
            "src/config.py",
            "src/utils/logger.py",
            "requirements.txt"
        ]
        
        for file_path in intent_files:
            src_path = Path(file_path)
            if src_path.exists():
                # Create subdirectories if needed
                dest_path = intent_dir / src_path.relative_to(Path("."))
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied {file_path}")
            else:
                print(f"✗ File not found: {file_path}")
        
        # Create training script for Kaggle
        kaggle_script = intent_dir / "train_intent.py"
        with open(kaggle_script, 'w') as f:
            f.write("""#!/usr/bin/env python3
\"\"\"
Intent Classifier Training Script for Kaggle
\"\"\"

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append('src')

# Set environment variables
os.environ["WANDB_DISABLED"] = "true"
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import and run training
from intent.train_classifier import main

if __name__ == "__main__":
    main()
""")
        print("✓ Created Kaggle training script")
        
        # Create zip file
        zip_path = Path("intent_training.zip")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in intent_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(intent_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✓ Created intent training zip: {zip_path}")
        return True
        
    except Exception as e:
        print(f"✗ Error creating intent training zip: {e}")
        return False
    finally:
        # Clean up temporary directory
        if intent_dir.exists():
            shutil.rmtree(intent_dir)

def create_ner_training_zip():
    """Create zip file for NER model training."""
    print("Creating NER training zip...")
    
    # Create temporary directory for NER training
    ner_dir = Path("temp_ner_training")
    ner_dir.mkdir(exist_ok=True)
    
    try:
        # Copy training data
        training_data_path = Path("data/training/entity_training_data.jsonl")
        if training_data_path.exists():
            shutil.copy2(training_data_path, ner_dir / "entity_training_data.jsonl")
            print(f"✓ Copied NER training data ({training_data_path.stat().st_size} bytes)")
        else:
            print("✗ NER training data not found")
            return False
        
        # Copy NER model files
        ner_files = [
            "src/ner/entity_extractor.py",
            "src/config.py",
            "src/utils/logger.py",
            "requirements.txt"
        ]
        
        for file_path in ner_files:
            src_path = Path(file_path)
            if src_path.exists():
                # Create subdirectories if needed
                dest_path = ner_dir / src_path.relative_to(Path("."))
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src_path, dest_path)
                print(f"✓ Copied {file_path}")
            else:
                print(f"✗ File not found: {file_path}")
        
        # Create training script for Kaggle
        kaggle_script = ner_dir / "train_ner.py"
        with open(kaggle_script, 'w') as f:
            f.write("""#!/usr/bin/env python3
\"\"\"
NER Model Training Script for Kaggle
\"\"\"

import os
import sys
from pathlib import Path

# Add src to path
sys.path.append('src')

# Set environment variables
os.environ["WANDB_DISABLED"] = "true"
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Import and run training
from ner.entity_extractor import EntityExtractor

def main():
    print("Starting NER model training...")
    
    # Initialize extractor and train
    extractor = EntityExtractor()
    
    # Train the model
    training_result = extractor.train_model(
        training_data_path="entity_training_data.jsonl",
        num_epochs=5,
        batch_size=8,
        learning_rate=5e-5
    )
    
    print(f"Training completed: {training_result}")
    
    # Save model info
    model_info = extractor.get_model_info()
    print(f"Model info: {model_info}")

if __name__ == "__main__":
    main()
""")
        print("✓ Created Kaggle training script")
        
        # Create zip file
        zip_path = Path("ner_training.zip")
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in ner_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(ner_dir)
                    zipf.write(file_path, arcname)
        
        print(f"✓ Created NER training zip: {zip_path}")
        return True
        
    except Exception as e:
        print(f"✗ Error creating NER training zip: {e}")
        return False
    finally:
        # Clean up temporary directory
        if ner_dir.exists():
            shutil.rmtree(ner_dir)

def main():
    """Create both training zip files."""
    print("Creating training zip files for Kaggle...")
    print("=" * 50)
    
    # Create intent training zip
    intent_success = create_intent_training_zip()
    print()
    
    # Create NER training zip
    ner_success = create_ner_training_zip()
    print()
    
    # Summary
    print("=" * 50)
    print("SUMMARY:")
    if intent_success:
        print("✓ Intent training zip created: intent_training.zip")
    else:
        print("✗ Intent training zip creation failed")
    
    if ner_success:
        print("✓ NER training zip created: ner_training.zip")
    else:
        print("✗ NER training zip creation failed")
    
    if intent_success and ner_success:
        print("\nBoth zip files are ready for Kaggle training!")
        print("Upload these files to your Kaggle kernel and run the training scripts.")
    else:
        print("\nSome zip files failed to create. Check the errors above.")

if __name__ == "__main__":
    main() 